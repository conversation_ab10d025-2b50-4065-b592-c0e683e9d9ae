<?php
include("Connecte.php");
?>
<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="icon.gif" type="image/x-icon">

  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="hide.css" />
  <link rel="stylesheet" href="all.min.css" />
  <title>Boutique</title>
</head>

<body>
  <?php
  $sqlpixel = "SELECT script FROM facebook_pixel ";
  $resultpixel = $conn->query($sqlpixel);

  if ($resultpixel->num_rows > 0) {
    $rowpixel = $resultpixel->fetch_assoc();
    $pixel_id = $rowpixel['script'];
  }
  ?>
  <script>
    ! function(f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '<?php echo $pixel_id; ?>');
    fbq('track', 'PageView');
  </script>
  <noscript>
    <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $pixel_id; ?>&ev=PageView&noscript=1" />
  </noscript>
  <div class="sidebar">
    <div class="" id="exit">✖</div>
    <div class="links">
      <ul>
        <a href="index.php">
          <li>Acceuil</li>
        </a>
        <a href="store.php">
          <li>Boutique</li>
        </a>
        <a href="livraison.php">
          <li>livraison</li>
        </a>
        <a href="paiment.php">
          <li>Payement</li>
        </a>
        <a href="Contact.php">
          <li>Contact</li>
        </a>
        <a href="about.php">
          <li>à Propos</li>
        </a>

      </ul>
    </div>
  </div>
  <div class="Bar">livraison disponible</div>
  <div class="navbar">
    <div class="right"><i class="fa-solid fa-bars"></i></div>
    <a href="index.php">
      <div class="meduim"></div>
    </a>
    <div class="left"><i class="fa-solid fa-magnifying-glass"></i></div>
  </div>


  <div class="container">
    <div class="title">
      <div class="first">Boutique</div>
    </div>

    <div class="search">
      <form action="store.php" method="get">
        <select name="id" id="category-select">
          <option value="" hidden>Chercher ...</option>
          <option value="">Tous</option>
          <?php
          $query_2 = "SELECT * FROM categories";
          $result_2 = mysqli_query($conn, $query_2);

          if (mysqli_num_rows($result_2) > 0) {
            while ($roc = mysqli_fetch_assoc($result_2)) {
              echo "<option value='" . $roc["CategoryID"] . "'>" . $roc["CategoryName"] . "</option>";
            }
          }
          ?>
        </select>
        <button type="submit" id="rechercher"><i class="fa-solid fa-magnifying-glass"></i></button>
    </div>
    </form>




    <div class="products">
      <?php
      include("Connecte.php");
      if (@$_GET['id']) {
        @$id = $_GET['id'];
        $query = "SELECT * FROM products WHERE CategoryID= $id ";
      } else {
        $query = "SELECT * FROM products";
      }
      $result = mysqli_query($conn, $query);

      while ($row = mysqli_fetch_assoc($result)) {
        $productName = htmlspecialchars($row['ProductName']);
        $productPrice = htmlspecialchars($row['Price']);
        $productPicture = $row['Picture'];
        $Category = $row['Picture'];
        $productName = htmlspecialchars($productName);
        $productPrice = htmlspecialchars($productPrice);

        $ctgQuery = "SELECT CategoryName FROM categories WHERE CategoryID = " . $row['CategoryID'];
        $ctgResult = mysqli_query($conn, $ctgQuery);
        $category = mysqli_fetch_assoc($ctgResult);
        $Ctg = $category['CategoryName'];
      ?>
        <div class="product">
          <div class="Picture" id="prod_1" style="background-image: url('data:image/jpeg;base64,<?php echo base64_encode($productPicture); ?>');"></div>
          <div class="description" style="text-align: center;padding-bottom: 20px;">
            <br>
            <h6><?php echo $Ctg; ?></h6>
            <h5><?php echo $productName; ?></h5>
            <p id="price"><?php echo $productPrice; ?> DA</p>
            <a href="Product.php?id=<?php echo htmlspecialchars($row['ProductID']); ?>">Acheter</a>
          </div>
        </div>
      <?php
      }
      ?>
    </div>
  </div>
</body>
<script src="page.js"></script>

</html>